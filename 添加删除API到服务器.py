#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为服务器添加删除API功能
"""

import paramiko
import json
import os
from datetime import datetime

def add_delete_api_to_server():
    """为服务器添加删除API"""
    
    # 服务器配置
    config = {
        'hostname': '**************',
        'port': 22,
        'username': 'root',
        'password': 'Zr20240101',  # 请确保密码正确
        'deploy_path': '/opt/license_manager'
    }
    
    # 删除API代码
    delete_api_code = '''
# ==================== 删除功能 API ====================

@app.route('/update/delete', methods=['DELETE'])
def delete_version():
    """删除指定版本"""
    try:
        version = request.args.get('version')
        key = request.args.get('key')
        device_id = request.args.get('device_id')
        
        # 验证管理员权限
        if key != "ADMIN_BYPASS" or device_id != "ADMIN-DEVICE-001":
            return jsonify({
                "success": False,
                "message": "权限不足，需要管理员权限"
            }), 403
        
        if not version:
            return jsonify({
                "success": False,
                "message": "缺少版本参数"
            }), 400
        
        # 查找并删除文件
        files_dir = os.path.join(UPDATE_DIR, "files")
        filename = f"amazon_blueprint_v{version}.exe"
        file_path = os.path.join(files_dir, filename)
        
        if not os.path.exists(file_path):
            return jsonify({
                "success": False,
                "message": f"版本 {version} 的文件不存在"
            }), 404
        
        # 删除文件
        os.remove(file_path)
        
        # 如果这是当前版本，清空版本信息
        if os.path.exists(VERSION_INFO_PATH):
            with open(VERSION_INFO_PATH, 'r', encoding='utf-8') as f:
                version_info = json.load(f)
            
            if version_info.get('version') == version:
                # 删除版本信息文件
                os.remove(VERSION_INFO_PATH)
                logging.info(f"Deleted current version info for version {version}")
        
        logging.info(f"Version {version} deleted successfully by admin")
        
        return jsonify({
            "success": True,
            "message": f"版本 {version} 删除成功",
            "deleted_version": version
        })
        
    except Exception as e:
        logging.error(f"Error in delete_version: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"删除失败: {str(e)}"
        }), 500

@app.route('/update/clear_all', methods=['DELETE'])
def clear_all_versions():
    """清空所有版本"""
    try:
        key = request.args.get('key')
        device_id = request.args.get('device_id')
        confirm = request.args.get('confirm')
        
        # 验证管理员权限
        if key != "ADMIN_BYPASS" or device_id != "ADMIN-DEVICE-001":
            return jsonify({
                "success": False,
                "message": "权限不足，需要管理员权限"
            }), 403
        
        # 验证确认参数
        if confirm != "yes_delete_all":
            return jsonify({
                "success": False,
                "message": "缺少确认参数"
            }), 400
        
        deleted_count = 0
        files_dir = os.path.join(UPDATE_DIR, "files")
        
        # 删除所有exe文件
        if os.path.exists(files_dir):
            for filename in os.listdir(files_dir):
                if filename.endswith('.exe') and 'amazon_blueprint_v' in filename:
                    file_path = os.path.join(files_dir, filename)
                    os.remove(file_path)
                    deleted_count += 1
                    logging.info(f"Deleted file: {filename}")
        
        # 删除版本信息文件
        if os.path.exists(VERSION_INFO_PATH):
            os.remove(VERSION_INFO_PATH)
            logging.info("Deleted version info file")
        
        logging.info(f"Cleared all versions - deleted {deleted_count} files")
        
        return jsonify({
            "success": True,
            "message": f"清空完成，已删除 {deleted_count} 个版本文件",
            "deleted_count": deleted_count
        })
        
    except Exception as e:
        logging.error(f"Error in clear_all_versions: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"清空失败: {str(e)}"
        }), 500

@app.route('/update/storage_info', methods=['GET'])
def get_storage_info():
    """获取存储信息"""
    try:
        key = request.args.get('key')
        device_id = request.args.get('device_id')
        
        # 验证管理员权限
        if key != "ADMIN_BYPASS" or device_id != "ADMIN-DEVICE-001":
            return jsonify({
                "success": False,
                "message": "权限不足，需要管理员权限"
            }), 403
        
        files_dir = os.path.join(UPDATE_DIR, "files")
        total_files = 0
        total_size = 0
        available_versions = []
        
        # 统计文件信息
        if os.path.exists(files_dir):
            for filename in os.listdir(files_dir):
                if filename.endswith('.exe') and 'amazon_blueprint_v' in filename:
                    file_path = os.path.join(files_dir, filename)
                    file_size = os.path.getsize(file_path)
                    total_files += 1
                    total_size += file_size
                    
                    # 提取版本号
                    try:
                        version = filename.split('amazon_blueprint_v')[1].split('.exe')[0]
                        available_versions.append(version)
                    except:
                        pass
        
        # 获取磁盘信息
        disk_info = {}
        try:
            import shutil
            total, used, free = shutil.disk_usage(UPDATE_DIR)
            disk_info = {
                "total_gb": total / (1024**3),
                "used_gb": used / (1024**3),
                "free_gb": free / (1024**3)
            }
        except:
            pass
        
        return jsonify({
            "success": True,
            "storage_info": {
                "total_files": total_files,
                "total_size_mb": total_size / (1024**2),
                "available_versions": sorted(available_versions),
                "disk_info": disk_info
            }
        })
        
    except Exception as e:
        logging.error(f"Error in get_storage_info: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"获取存储信息失败: {str(e)}"
        }), 500

@app.route('/update/version_details', methods=['GET'])
def get_version_details():
    """获取版本详细信息"""
    try:
        version = request.args.get('version')
        key = request.args.get('key')
        device_id = request.args.get('device_id')
        
        # 验证管理员权限
        if key != "ADMIN_BYPASS" or device_id != "ADMIN-DEVICE-001":
            return jsonify({
                "success": False,
                "message": "权限不足，需要管理员权限"
            }), 403
        
        if not version:
            return jsonify({
                "success": False,
                "message": "缺少版本参数"
            }), 400
        
        # 查找文件
        files_dir = os.path.join(UPDATE_DIR, "files")
        filename = f"amazon_blueprint_v{version}.exe"
        file_path = os.path.join(files_dir, filename)
        
        if not os.path.exists(file_path):
            return jsonify({
                "success": False,
                "message": f"版本 {version} 的文件不存在"
            }), 404
        
        # 获取文件信息
        file_size = os.path.getsize(file_path)
        file_hash = calculate_file_hash(file_path)
        
        # 获取版本信息
        version_details = {
            "version": version,
            "file_size": file_size,
            "file_hash": file_hash,
            "filename": filename
        }
        
        # 如果是当前版本，获取更多信息
        if os.path.exists(VERSION_INFO_PATH):
            with open(VERSION_INFO_PATH, 'r', encoding='utf-8') as f:
                version_info = json.load(f)
            
            if version_info.get('version') == version:
                version_details.update({
                    "changelog": version_info.get('changelog', ''),
                    "upload_time": version_info.get('upload_time', ''),
                    "download_count": version_info.get('download_count', 0)
                })
        
        return jsonify({
            "success": True,
            "version_details": version_details
        })
        
    except Exception as e:
        logging.error(f"Error in get_version_details: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"获取版本详情失败: {str(e)}"
        }), 500

'''
    
    try:
        print("🚀 开始为服务器添加删除API...")
        
        # 连接服务器
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        print(f"🔗 连接服务器 {config['hostname']}...")
        client.connect(
            hostname=config['hostname'],
            port=config['port'],
            username=config['username'],
            password=config['password'],
            timeout=30
        )
        print("   ✅ 服务器连接成功")
        
        # 备份原文件
        backup_time = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_command = f"cp {config['deploy_path']}/license_server.py {config['deploy_path']}/license_server.py.backup_delete_api_{backup_time}"
        
        stdin, stdout, stderr = client.exec_command(backup_command)
        exit_status = stdout.channel.recv_exit_status()
        
        if exit_status == 0:
            print("   ✅ 原文件已备份")
        else:
            print("   ⚠️ 备份失败，但继续执行")
        
        # 添加删除API代码
        print("📝 添加删除API代码...")
        
        add_api_command = f'''
cd {config['deploy_path']}

# 在文件末尾添加删除API（在if __name__ == '__main__'之前）
cat >> license_server_temp.py << 'EOF'
{delete_api_code}
EOF

# 将新代码插入到原文件中
sed '/if __name__ == .__main__.:/i\\{delete_api_code}' license_server.py > license_server_new.py
mv license_server_new.py license_server.py

# 清理临时文件
rm -f license_server_temp.py
'''
        
        stdin, stdout, stderr = client.exec_command(add_api_command)
        exit_status = stdout.channel.recv_exit_status()
        
        if exit_status == 0:
            print("   ✅ 删除API代码已添加")
        else:
            error = stderr.read().decode('utf-8')
            print(f"   ❌ 添加失败: {error}")
            return False
        
        # 重启服务
        print("🔄 重启服务...")
        restart_command = "systemctl restart license-manager"
        
        stdin, stdout, stderr = client.exec_command(restart_command)
        exit_status = stdout.channel.recv_exit_status()
        
        if exit_status == 0:
            print("   ✅ 服务重启成功")
        else:
            error = stderr.read().decode('utf-8')
            print(f"   ⚠️ 重启失败: {error}")
            print("   请手动重启服务: sudo systemctl restart license-manager")
        
        # 检查服务状态
        print("🔍 检查服务状态...")
        status_command = "systemctl status license-manager --no-pager -l"
        
        stdin, stdout, stderr = client.exec_command(status_command)
        status_output = stdout.read().decode('utf-8')
        
        if "active (running)" in status_output:
            print("   ✅ 服务运行正常")
        else:
            print("   ⚠️ 服务状态异常")
            print(f"   状态信息: {status_output}")
        
        client.close()
        
        print("\n🎉 删除API添加完成！")
        print("\n新增API接口：")
        print("• DELETE /update/delete - 删除指定版本")
        print("• DELETE /update/clear_all - 清空所有版本")
        print("• GET /update/storage_info - 获取存储信息")
        print("• GET /update/version_details - 获取版本详情")
        print("\n现在可以使用exe文件管理工具的删除功能了！")
        
        return True
        
    except Exception as e:
        print(f"❌ 添加删除API失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 为服务器添加删除API功能")
    print("=" * 50)
    
    success = add_delete_api_to_server()
    
    if success:
        print("\n✅ 操作完成！删除功能已启用。")
    else:
        print("\n❌ 操作失败！请检查错误信息。")
