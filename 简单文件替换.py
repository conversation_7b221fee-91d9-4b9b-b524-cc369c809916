#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单文件替换工具
直接替换exe文件，无需复杂的更新流程
"""

import os
import sys
import glob
import shutil
import subprocess
import time
from datetime import datetime

def find_exe_files():
    """查找当前目录下的exe文件"""
    exe_files = glob.glob("*.exe")
    return exe_files

def find_new_files():
    """查找可能的新版本文件"""
    # 查找常见的新文件模式
    patterns = [
        "*.new",
        "*.update", 
        "*_new.exe",
        "*_update.exe",
        "temp_*.exe",
        "new_*.exe"
    ]
    
    new_files = []
    for pattern in patterns:
        new_files.extend(glob.glob(pattern))
    
    return new_files

def kill_process(exe_name):
    """终止指定的exe进程"""
    try:
        subprocess.run(['taskkill', '/f', '/im', exe_name], 
                      capture_output=True, text=True)
        print(f"   ✅ 已终止进程: {exe_name}")
        time.sleep(2)  # 等待进程完全退出
        return True
    except:
        print(f"   ⚠️ 未找到运行中的进程: {exe_name}")
        return False

def backup_file(file_path):
    """备份文件"""
    backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    try:
        shutil.copy2(file_path, backup_path)
        print(f"   ✅ 已备份到: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"   ❌ 备份失败: {e}")
        return None

def replace_file(old_file, new_file):
    """替换文件"""
    try:
        # 1. 备份原文件
        backup_path = backup_file(old_file)
        if not backup_path:
            return False
        
        # 2. 删除原文件
        os.remove(old_file)
        print(f"   ✅ 已删除原文件: {old_file}")
        
        # 3. 复制新文件
        shutil.copy2(new_file, old_file)
        print(f"   ✅ 已复制新文件: {new_file} -> {old_file}")
        
        # 4. 验证新文件
        if os.path.exists(old_file):
            size_mb = os.path.getsize(old_file) / (1024 * 1024)
            print(f"   ✅ 新文件大小: {size_mb:.1f}MB")
            return True
        else:
            print(f"   ❌ 替换后文件不存在")
            return False
            
    except Exception as e:
        print(f"   ❌ 替换失败: {e}")
        # 尝试恢复备份
        if backup_path and os.path.exists(backup_path):
            try:
                shutil.copy2(backup_path, old_file)
                print(f"   ✅ 已恢复备份文件")
            except:
                print(f"   ❌ 恢复备份也失败")
        return False

def start_program(exe_file):
    """启动程序"""
    try:
        subprocess.Popen([exe_file])
        print(f"   ✅ 已启动程序: {exe_file}")
        return True
    except Exception as e:
        print(f"   ❌ 启动失败: {e}")
        return False

def interactive_replace():
    """交互式替换"""
    print("🔧 简单文件替换工具")
    print("=" * 50)
    
    # 1. 查找现有exe文件
    print("1. 查找现有exe文件...")
    exe_files = find_exe_files()
    
    if not exe_files:
        print("   ❌ 未找到exe文件")
        return False
    
    print("   发现的exe文件:")
    for i, exe_file in enumerate(exe_files):
        size_mb = os.path.getsize(exe_file) / (1024 * 1024)
        mod_time = datetime.fromtimestamp(os.path.getmtime(exe_file))
        print(f"   [{i+1}] {exe_file} ({size_mb:.1f}MB, {mod_time.strftime('%Y-%m-%d %H:%M')})")
    
    # 2. 选择要替换的文件
    if len(exe_files) == 1:
        target_exe = exe_files[0]
        print(f"   自动选择: {target_exe}")
    else:
        try:
            choice = int(input("   请选择要替换的文件编号: ")) - 1
            if 0 <= choice < len(exe_files):
                target_exe = exe_files[choice]
            else:
                print("   ❌ 无效选择")
                return False
        except:
            print("   ❌ 输入无效")
            return False
    
    # 3. 查找新文件
    print("\n2. 查找新版本文件...")
    new_files = find_new_files()
    
    if not new_files:
        print("   ❌ 未找到新版本文件")
        print("   💡 请将新版本文件重命名为以下格式之一:")
        print("      - 文件名.new")
        print("      - 文件名_new.exe") 
        print("      - new_文件名.exe")
        return False
    
    print("   发现的新文件:")
    for i, new_file in enumerate(new_files):
        size_mb = os.path.getsize(new_file) / (1024 * 1024)
        mod_time = datetime.fromtimestamp(os.path.getmtime(new_file))
        print(f"   [{i+1}] {new_file} ({size_mb:.1f}MB, {mod_time.strftime('%Y-%m-%d %H:%M')})")
    
    # 4. 选择新文件
    if len(new_files) == 1:
        source_file = new_files[0]
        print(f"   自动选择: {source_file}")
    else:
        try:
            choice = int(input("   请选择新版本文件编号: ")) - 1
            if 0 <= choice < len(new_files):
                source_file = new_files[choice]
            else:
                print("   ❌ 无效选择")
                return False
        except:
            print("   ❌ 输入无效")
            return False
    
    # 5. 确认替换
    print(f"\n3. 确认替换操作:")
    print(f"   原文件: {target_exe}")
    print(f"   新文件: {source_file}")
    
    confirm = input("   确认替换? (y/n): ").lower().strip()
    if confirm not in ['y', 'yes', '是']:
        print("   ❌ 用户取消操作")
        return False
    
    # 6. 执行替换
    print("\n4. 执行替换...")
    
    # 终止进程
    exe_name = os.path.basename(target_exe)
    kill_process(exe_name)
    
    # 替换文件
    if replace_file(target_exe, source_file):
        print("   ✅ 文件替换成功")
        
        # 清理新文件
        try:
            os.remove(source_file)
            print(f"   ✅ 已清理临时文件: {source_file}")
        except:
            print(f"   ⚠️ 清理临时文件失败: {source_file}")
        
        # 启动新程序
        print("\n5. 启动新程序...")
        if start_program(target_exe):
            print("\n🎉 替换完成！新程序已启动")
            return True
        else:
            print("\n⚠️ 替换成功但启动失败，请手动启动程序")
            return True
    else:
        print("   ❌ 文件替换失败")
        return False

def auto_replace():
    """自动替换（如果只有一个exe和一个新文件）"""
    exe_files = find_exe_files()
    new_files = find_new_files()
    
    if len(exe_files) == 1 and len(new_files) == 1:
        target_exe = exe_files[0]
        source_file = new_files[0]
        
        print("🔧 自动文件替换")
        print("=" * 50)
        print(f"原文件: {target_exe}")
        print(f"新文件: {source_file}")
        
        # 终止进程
        exe_name = os.path.basename(target_exe)
        kill_process(exe_name)
        
        # 替换文件
        if replace_file(target_exe, source_file):
            # 清理新文件
            try:
                os.remove(source_file)
            except:
                pass
            
            # 启动新程序
            start_program(target_exe)
            print("\n🎉 自动替换完成！")
            return True
    
    return False

def main():
    """主函数"""
    # 尝试自动替换
    if not auto_replace():
        # 如果自动替换失败，使用交互式替换
        interactive_replace()
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
