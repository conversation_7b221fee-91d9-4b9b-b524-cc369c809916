#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断更新问题脚本
检查更新日志和程序状态，找出卡住的原因
"""

import os
import sys
import json
import glob
from datetime import datetime
from pathlib import Path

def check_update_logs():
    """检查更新日志"""
    print("=" * 60)
    print("🔍 检查更新日志")
    print("=" * 60)
    
    # 检查用户目录下的更新日志
    home_dir = os.path.expanduser("~")
    log_files = [
        os.path.join(home_dir, "update_log.txt"),
        os.path.join(home_dir, ".amazon_last_update.json"),
        "update_log.txt",
        "build.log"
    ]
    
    for log_file in log_files:
        if os.path.exists(log_file):
            print(f"\n📄 发现日志文件: {log_file}")
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if content.strip():
                        print("内容:")
                        print("-" * 40)
                        print(content[-2000:])  # 显示最后2000字符
                        print("-" * 40)
                    else:
                        print("文件为空")
            except Exception as e:
                print(f"读取失败: {e}")
        else:
            print(f"❌ 未找到: {log_file}")

def check_backup_files():
    """检查备份文件"""
    print("\n" + "=" * 60)
    print("🔍 检查备份文件")
    print("=" * 60)
    
    current_dir = os.getcwd()
    backup_patterns = [
        "*.backup",
        "*.exe.backup",
        "亚马逊蓝图工具.exe.backup"
    ]
    
    for pattern in backup_patterns:
        backup_files = glob.glob(pattern)
        if backup_files:
            for backup_file in backup_files:
                print(f"📦 发现备份文件: {backup_file}")
                stat = os.stat(backup_file)
                size_mb = stat.st_size / (1024 * 1024)
                mod_time = datetime.fromtimestamp(stat.st_mtime)
                print(f"   大小: {size_mb:.1f}MB")
                print(f"   修改时间: {mod_time}")
        else:
            print(f"❌ 未找到匹配 {pattern} 的备份文件")

def check_current_exe():
    """检查当前exe文件状态"""
    print("\n" + "=" * 60)
    print("🔍 检查当前exe文件")
    print("=" * 60)
    
    exe_files = glob.glob("*.exe")
    if exe_files:
        for exe_file in exe_files:
            print(f"📱 发现exe文件: {exe_file}")
            try:
                stat = os.stat(exe_file)
                size_mb = stat.st_size / (1024 * 1024)
                mod_time = datetime.fromtimestamp(stat.st_mtime)
                print(f"   大小: {size_mb:.1f}MB")
                print(f"   修改时间: {mod_time}")
                
                # 检查是否可以执行
                if os.access(exe_file, os.X_OK):
                    print("   ✅ 文件可执行")
                else:
                    print("   ❌ 文件不可执行")
                    
            except Exception as e:
                print(f"   ❌ 检查失败: {e}")
    else:
        print("❌ 未找到exe文件")

def check_temp_files():
    """检查临时文件"""
    print("\n" + "=" * 60)
    print("🔍 检查临时文件")
    print("=" * 60)
    
    temp_patterns = [
        "update_*.py",
        "temp_*.exe",
        "*.tmp"
    ]
    
    for pattern in temp_patterns:
        temp_files = glob.glob(pattern)
        if temp_files:
            for temp_file in temp_files:
                print(f"🗂️ 发现临时文件: {temp_file}")
                try:
                    stat = os.stat(temp_file)
                    mod_time = datetime.fromtimestamp(stat.st_mtime)
                    print(f"   修改时间: {mod_time}")
                except Exception as e:
                    print(f"   检查失败: {e}")

def check_processes():
    """检查相关进程"""
    print("\n" + "=" * 60)
    print("🔍 检查相关进程")
    print("=" * 60)
    
    try:
        import psutil
        
        # 查找相关进程
        target_names = ["亚马逊蓝图工具.exe", "python.exe", "pythonw.exe"]
        
        for proc in psutil.process_iter(['pid', 'name', 'exe', 'cmdline']):
            try:
                proc_info = proc.info
                if any(name.lower() in proc_info['name'].lower() for name in target_names):
                    print(f"🔄 发现相关进程:")
                    print(f"   PID: {proc_info['pid']}")
                    print(f"   名称: {proc_info['name']}")
                    if proc_info['exe']:
                        print(f"   路径: {proc_info['exe']}")
                    if proc_info['cmdline']:
                        print(f"   命令行: {' '.join(proc_info['cmdline'])}")
                    print()
            except:
                continue
                
    except ImportError:
        print("❌ psutil未安装，无法检查进程")
        print("可以手动打开任务管理器检查是否有相关进程在运行")

def check_path_issues():
    """检查路径问题"""
    print("\n" + "=" * 60)
    print("🔍 检查路径问题")
    print("=" * 60)
    
    current_path = os.getcwd()
    print(f"当前工作目录: {current_path}")
    
    # 检查路径中的问题字符
    problematic_chars = ['(', ')', ' ', '中', '文']
    issues = []
    
    for char in problematic_chars:
        if char in current_path:
            issues.append(f"包含字符 '{char}'")
    
    if issues:
        print("⚠️ 发现路径问题:")
        for issue in issues:
            print(f"   - {issue}")
        print("\n💡 建议:")
        print("   - 将程序移动到简单的英文路径，如 C:\\AmazonTool\\")
        print("   - 避免使用中文字符和特殊符号")
    else:
        print("✅ 路径看起来正常")

def main():
    """主函数"""
    print("🔧 亚马逊蓝图工具 - 更新问题诊断")
    print(f"诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    check_update_logs()
    check_backup_files()
    check_current_exe()
    check_temp_files()
    check_processes()
    check_path_issues()
    
    print("\n" + "=" * 60)
    print("🎯 诊断完成")
    print("=" * 60)
    print("请查看上述信息，找出问题原因")
    print("如果需要修复，请运行 '修复更新问题.py' 脚本")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
