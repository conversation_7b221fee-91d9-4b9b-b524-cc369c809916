#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紧急停止更新循环 - 立即修复无限更新问题
"""

import os
import shutil
import sys

def emergency_fix():
    """紧急修复：停止自动更新循环"""
    print("🚨 紧急修复：停止自动更新循环")
    print("=" * 50)
    
    # 方法1：临时禁用自动更新检查
    fix_auto_update_check()
    
    # 方法2：清理临时目录
    clean_temp_directories()
    
    # 方法3：创建版本锁定文件
    create_version_lock()
    
    print("\n✅ 紧急修复完成！")
    print("现在可以正常使用程序，不会再出现无限更新循环")

def fix_auto_update_check():
    """修复自动更新检查"""
    print("\n🔧 修复自动更新检查...")
    
    try:
        # 备份原文件
        if os.path.exists('license_client.py'):
            shutil.copy2('license_client.py', 'license_client.py.backup_emergency')
            print("✅ 已备份 license_client.py")
            
            # 读取文件内容
            with open('license_client.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 临时禁用自动更新检查
            if 'def check_for_updates(self):' in content:
                # 在函数开头添加返回语句
                content = content.replace(
                    'def check_for_updates(self):',
                    '''def check_for_updates(self):
        """检查程序更新（自动启动时调用） - 临时禁用"""
        print("🛑 自动更新已临时禁用，避免无限循环")
        return  # 临时禁用自动更新检查
        
    def check_for_updates_disabled(self):'''
                )
                
                # 保存修改后的文件
                with open('license_client.py', 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print("✅ 已临时禁用自动更新检查")
                return True
        
        print("⚠️ 未找到 license_client.py 文件")
        return False
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def clean_temp_directories():
    """清理PyInstaller临时目录"""
    print("\n🧹 清理临时目录...")
    
    try:
        import tempfile
        import glob
        
        temp_dir = tempfile.gettempdir()
        print(f"临时目录: {temp_dir}")
        
        # 清理 _MEI* 目录
        mei_dirs = glob.glob(os.path.join(temp_dir, "_MEI*"))
        cleaned_count = 0
        
        for mei_dir in mei_dirs:
            try:
                shutil.rmtree(mei_dir, ignore_errors=True)
                print(f"🗑️ 清理: {mei_dir}")
                cleaned_count += 1
            except Exception as e:
                print(f"⚠️ 清理失败 {mei_dir}: {e}")
        
        print(f"✅ 清理了 {cleaned_count} 个临时目录")
        return True
        
    except Exception as e:
        print(f"❌ 清理临时目录失败: {e}")
        return False

def create_version_lock():
    """创建版本锁定文件"""
    print("\n🔒 创建版本锁定文件...")
    
    try:
        # 创建版本锁定文件，防止自动更新
        lock_content = {
            "version": "2.1.1",  # 设置为最新版本
            "auto_update_disabled": True,
            "lock_reason": "Emergency fix for infinite update loop",
            "created_time": str(datetime.now())
        }
        
        import json
        from datetime import datetime
        
        with open('version_lock.json', 'w', encoding='utf-8') as f:
            json.dump(lock_content, f, indent=2, ensure_ascii=False)
        
        print("✅ 已创建版本锁定文件 version_lock.json")
        return True
        
    except Exception as e:
        print(f"❌ 创建版本锁定文件失败: {e}")
        return False

def restore_backup():
    """恢复备份文件"""
    print("\n🔄 恢复备份文件...")
    
    try:
        if os.path.exists('license_client.py.backup_emergency'):
            shutil.copy2('license_client.py.backup_emergency', 'license_client.py')
            print("✅ 已恢复 license_client.py 备份")
            return True
        else:
            print("⚠️ 未找到备份文件")
            return False
            
    except Exception as e:
        print(f"❌ 恢复备份失败: {e}")
        return False

def main():
    """主函数"""
    print("🚨 exe无限更新循环紧急修复工具")
    print("=" * 60)
    print("问题: 程序启动后不断检查更新并重启")
    print("原因: 版本号硬编码导致的无限更新循环")
    print("=" * 60)
    
    while True:
        print("\n请选择操作:")
        print("1. 🚨 紧急修复（停止无限更新循环）")
        print("2. 🔄 恢复备份文件")
        print("3. 🧹 仅清理临时目录")
        print("4. ❌ 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == '1':
            emergency_fix()
            break
        elif choice == '2':
            restore_backup()
        elif choice == '3':
            clean_temp_directories()
        elif choice == '4':
            print("退出程序")
            break
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
