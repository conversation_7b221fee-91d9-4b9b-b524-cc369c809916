#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紧急停止更新循环 - 立即修复无限更新问题
"""

import os
import shutil
import sys

def emergency_fix():
    """紧急修复：修复无限更新循环但保留自动更新功能"""
    print("🚨 紧急修复：修复无限更新循环")
    print("=" * 50)

    # 方法1：修复版本号硬编码问题
    fix_auto_update_check()

    # 方法2：清理临时目录
    clean_temp_directories()

    # 方法3：创建版本配置文件
    create_version_config_file()

    print("\n✅ 紧急修复完成！")
    print("✅ 保留了自动更新功能，但修复了无限循环问题")
    print("✅ 现在程序会从配置文件读取版本号，避免硬编码问题")

def fix_auto_update_check():
    """修复自动更新检查 - 保留功能但修复循环问题"""
    print("\n🔧 修复自动更新检查...")

    try:
        # 备份原文件
        if os.path.exists('license_client.py'):
            shutil.copy2('license_client.py', 'license_client.py.backup_emergency')
            print("✅ 已备份 license_client.py")

            # 读取文件内容
            with open('license_client.py', 'r', encoding='utf-8') as f:
                content = f.read()

            # 修复硬编码版本号问题（保留自动更新功能）
            if 'current_version = "2.1.0"  # 当前版本号' in content:
                # 创建版本配置文件
                create_version_config_file()

                # 替换硬编码版本号为动态读取
                content = content.replace(
                    'current_version = "2.1.0"  # 当前版本号',
                    '''# 从配置文件读取当前版本号，避免硬编码导致的循环更新
                    current_version = get_version_from_config()'''
                )

                # 添加版本读取函数
                if 'def get_version_from_config():' not in content:
                    version_function = '''
def get_version_from_config():
    """从配置文件读取版本号"""
    try:
        import json
        if os.path.exists('version_config.json'):
            with open('version_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get('current_version', '2.1.1')
        return '2.1.1'  # 默认最新版本
    except:
        return '2.1.1'  # 出错时返回最新版本

'''
                    # 在类定义前添加函数
                    content = content.replace(
                        'class SimpleClient:',
                        version_function + 'class SimpleClient:'
                    )

                # 保存修改后的文件
                with open('license_client.py', 'w', encoding='utf-8') as f:
                    f.write(content)

                print("✅ 已修复版本号硬编码问题，保留自动更新功能")
                return True

        print("⚠️ 未找到 license_client.py 文件")
        return False

    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def clean_temp_directories():
    """清理PyInstaller临时目录"""
    print("\n🧹 清理临时目录...")
    
    try:
        import tempfile
        import glob
        
        temp_dir = tempfile.gettempdir()
        print(f"临时目录: {temp_dir}")
        
        # 清理 _MEI* 目录
        mei_dirs = glob.glob(os.path.join(temp_dir, "_MEI*"))
        cleaned_count = 0
        
        for mei_dir in mei_dirs:
            try:
                shutil.rmtree(mei_dir, ignore_errors=True)
                print(f"🗑️ 清理: {mei_dir}")
                cleaned_count += 1
            except Exception as e:
                print(f"⚠️ 清理失败 {mei_dir}: {e}")
        
        print(f"✅ 清理了 {cleaned_count} 个临时目录")
        return True
        
    except Exception as e:
        print(f"❌ 清理临时目录失败: {e}")
        return False

def create_version_config_file():
    """创建版本配置文件"""
    print("\n📝 创建版本配置文件...")

    try:
        import json
        from datetime import datetime

        # 创建版本配置文件，记录当前版本
        version_config = {
            "current_version": "2.1.1",  # 设置为最新版本，避免循环更新
            "last_update_check": str(datetime.now()),
            "auto_update_enabled": True,  # 保持自动更新功能
            "update_server_url": "http://198.23.135.176:5000",
            "fix_reason": "Fix infinite update loop by using external version config"
        }

        with open('version_config.json', 'w', encoding='utf-8') as f:
            json.dump(version_config, f, indent=2, ensure_ascii=False)

        print("✅ 已创建版本配置文件 version_config.json")
        return True

    except Exception as e:
        print(f"❌ 创建版本配置文件失败: {e}")
        return False

def restore_backup():
    """恢复备份文件"""
    print("\n🔄 恢复备份文件...")
    
    try:
        if os.path.exists('license_client.py.backup_emergency'):
            shutil.copy2('license_client.py.backup_emergency', 'license_client.py')
            print("✅ 已恢复 license_client.py 备份")
            return True
        else:
            print("⚠️ 未找到备份文件")
            return False
            
    except Exception as e:
        print(f"❌ 恢复备份失败: {e}")
        return False

def main():
    """主函数"""
    print("🚨 exe无限更新循环修复工具")
    print("=" * 60)
    print("问题: 程序启动后不断检查更新并重启")
    print("原因: 版本号硬编码导致的无限更新循环")
    print("方案: 修复版本号管理，保留自动更新功能")
    print("=" * 60)

    while True:
        print("\n请选择操作:")
        print("1. 🔧 修复无限循环（保留自动更新功能）")
        print("2. 🔄 恢复备份文件")
        print("3. 🧹 仅清理临时目录")
        print("4. 📝 仅创建版本配置文件")
        print("5. ❌ 退出")

        choice = input("\n请输入选择 (1-5): ").strip()

        if choice == '1':
            emergency_fix()
            break
        elif choice == '2':
            restore_backup()
        elif choice == '3':
            clean_temp_directories()
        elif choice == '4':
            create_version_config_file()
        elif choice == '5':
            print("退出程序")
            break
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
