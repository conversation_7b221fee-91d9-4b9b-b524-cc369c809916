#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紧急修复卡住的更新问题
"""

import os
import sys
import shutil
import json
import tempfile
import psutil
from datetime import datetime

def kill_related_processes():
    """结束相关进程"""
    print("🔄 结束相关进程...")
    
    target_names = ['license_client.exe', 'amazon', 'blueprint']
    killed = []
    
    try:
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                proc_name = proc.info['name'].lower()
                proc_exe = proc.info['exe'] or ''
                
                for target in target_names:
                    if target in proc_name or target in proc_exe.lower():
                        print(f"结束进程: {proc.info['name']} (PID: {proc.info['pid']})")
                        proc.terminate()
                        killed.append(proc.info['name'])
                        break
            except:
                continue
                
        if killed:
            print(f"✅ 已结束 {len(killed)} 个进程")
        else:
            print("✅ 没有需要结束的进程")
            
    except Exception as e:
        print(f"❌ 结束进程失败: {e}")

def clean_temp_files():
    """清理临时文件"""
    print("\n🧹 清理临时文件...")
    
    temp_dir = tempfile.gettempdir()
    cleaned = 0
    
    try:
        for file in os.listdir(temp_dir):
            if ('amazon' in file.lower() or 'blueprint' in file.lower() or 
                'ultimate_update' in file.lower() or 'update_script' in file.lower()):
                file_path = os.path.join(temp_dir, file)
                try:
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                        cleaned += 1
                except:
                    pass
                    
        print(f"✅ 已清理 {cleaned} 个临时文件")
        
    except Exception as e:
        print(f"❌ 清理临时文件失败: {e}")

def reset_version_config():
    """重置版本配置"""
    print("\n🔧 重置版本配置...")
    
    try:
        config_dir = os.path.join(os.path.expanduser("~"), "AppData", "Local", "AmazonLicenseClient")
        config_file = os.path.join(config_dir, "version_config.json")
        
        if os.path.exists(config_file):
            # 备份当前配置
            backup_file = config_file + ".backup"
            shutil.copy2(config_file, backup_file)
            print(f"✅ 已备份配置到: {backup_file}")
        
        # 创建新配置
        os.makedirs(config_dir, exist_ok=True)
        config_data = {
            "current_version": "2.1.2",
            "last_updated": datetime.now().isoformat(),
            "auto_check_enabled": True,
            "update_server_url": "http://your-server.com/api/check_update",
            "license_server_url": "http://your-server.com/api/check_license_update"
        }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
        
        print("✅ 版本配置已重置")
        
    except Exception as e:
        print(f"❌ 重置版本配置失败: {e}")

def check_exe_files():
    """检查exe文件"""
    print("\n🔍 检查exe文件...")
    
    current_dir = os.getcwd()
    exe_files = []
    
    try:
        for file in os.listdir(current_dir):
            if file.endswith('.exe') and ('license' in file.lower() or 'amazon' in file.lower()):
                file_path = os.path.join(current_dir, file)
                size = os.path.getsize(file_path)
                mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                exe_files.append({
                    'name': file,
                    'path': file_path,
                    'size': size,
                    'mtime': mtime
                })
        
        if exe_files:
            print("📁 发现exe文件:")
            for exe in exe_files:
                print(f"  {exe['name']} ({exe['size']} bytes, {exe['mtime']})")
        else:
            print("❌ 没有发现exe文件")
            
    except Exception as e:
        print(f"❌ 检查exe文件失败: {e}")
    
    return exe_files

def provide_next_steps():
    """提供下一步操作"""
    print("\n📋 下一步操作:")
    print("=" * 50)
    
    print("1. 🔄 重新启动程序:")
    print("   - 双击最新的exe文件")
    print("   - 程序应该正常启动，不再卡在更新")
    
    print("\n2. 🧪 测试更新功能:")
    print("   - 如果程序正常启动，可以测试手动检查更新")
    print("   - 新版本包含改进的更新逻辑")
    
    print("\n3. 🚨 如果仍有问题:")
    print("   - 检查是否有杀毒软件阻止")
    print("   - 确保有足够的磁盘空间")
    print("   - 检查文件权限")
    
    print("\n4. 📞 联系支持:")
    print("   - 如果问题持续，请提供错误日志")
    print("   - 新版本会生成详细的更新日志")

def main():
    """主修复函数"""
    print("🚨 紧急修复工具 - 解决更新卡住问题")
    print("=" * 60)
    
    print("⚠️  警告：此工具将结束相关进程并清理临时文件")
    response = input("是否继续？(y/N): ").strip().lower()
    
    if response != 'y':
        print("❌ 操作已取消")
        return
    
    # 执行修复步骤
    kill_related_processes()
    clean_temp_files()
    reset_version_config()
    exe_files = check_exe_files()
    provide_next_steps()
    
    print("\n" + "=" * 60)
    print("🎉 紧急修复完成")
    
    if exe_files:
        latest_exe = max(exe_files, key=lambda x: x['mtime'])
        print(f"\n🎯 建议启动最新的exe文件:")
        print(f"   {latest_exe['name']} ({latest_exe['mtime']})")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
