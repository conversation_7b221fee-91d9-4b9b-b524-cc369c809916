#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版本管理机制 - 彻底解决无限更新循环问题
"""

import os
import json
import shutil
from datetime import datetime

def create_version_config():
    """创建版本配置文件"""
    print("📝 创建版本配置文件...")
    
    version_config = {
        "current_version": "2.1.1",  # 设置为最新版本
        "last_update_check": str(datetime.now()),
        "auto_update_enabled": True,
        "update_server_url": "http://**************:5000",
        "version_history": [
            {
                "version": "2.1.0",
                "date": "2024-01-01",
                "description": "初始版本"
            },
            {
                "version": "2.1.1", 
                "date": str(datetime.now().date()),
                "description": "修复无限更新循环问题"
            }
        ]
    }
    
    try:
        with open('version_config.json', 'w', encoding='utf-8') as f:
            json.dump(version_config, f, indent=2, ensure_ascii=False)
        
        print("✅ 版本配置文件创建成功: version_config.json")
        return True
        
    except Exception as e:
        print(f"❌ 创建版本配置文件失败: {e}")
        return False

def create_version_manager():
    """创建版本管理器模块"""
    print("🔧 创建版本管理器...")
    
    version_manager_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
版本管理器 - 统一管理程序版本信息
"""

import os
import json
from datetime import datetime

class VersionManager:
    """版本管理器"""
    
    def __init__(self, config_file="version_config.json"):
        self.config_file = config_file
        self.config = self.load_config()
    
    def load_config(self):
        """加载版本配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # 创建默认配置
                return self.create_default_config()
        except Exception as e:
            print(f"加载版本配置失败: {e}")
            return self.create_default_config()
    
    def create_default_config(self):
        """创建默认配置"""
        default_config = {
            "current_version": "2.1.1",
            "last_update_check": str(datetime.now()),
            "auto_update_enabled": True,
            "update_server_url": "http://**************:5000"
        }
        self.save_config(default_config)
        return default_config
    
    def save_config(self, config=None):
        """保存版本配置"""
        try:
            config_to_save = config or self.config
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存版本配置失败: {e}")
            return False
    
    def get_current_version(self):
        """获取当前版本"""
        return self.config.get("current_version", "2.1.0")
    
    def set_current_version(self, version):
        """设置当前版本"""
        self.config["current_version"] = version
        self.config["last_update_check"] = str(datetime.now())
        return self.save_config()
    
    def is_auto_update_enabled(self):
        """检查是否启用自动更新"""
        return self.config.get("auto_update_enabled", True)
    
    def disable_auto_update(self):
        """禁用自动更新"""
        self.config["auto_update_enabled"] = False
        return self.save_config()
    
    def enable_auto_update(self):
        """启用自动更新"""
        self.config["auto_update_enabled"] = True
        return self.save_config()

# 全局版本管理器实例
version_manager = VersionManager()

def get_current_version():
    """获取当前版本（便捷函数）"""
    return version_manager.get_current_version()

def set_current_version(version):
    """设置当前版本（便捷函数）"""
    return version_manager.set_current_version(version)

def is_auto_update_enabled():
    """检查是否启用自动更新（便捷函数）"""
    return version_manager.is_auto_update_enabled()
'''
    
    try:
        with open('version_manager.py', 'w', encoding='utf-8') as f:
            f.write(version_manager_code)
        
        print("✅ 版本管理器创建成功: version_manager.py")
        return True
        
    except Exception as e:
        print(f"❌ 创建版本管理器失败: {e}")
        return False

def fix_license_client():
    """修复license_client.py中的版本管理"""
    print("🔧 修复license_client.py中的版本管理...")
    
    try:
        if not os.path.exists('license_client.py'):
            print("❌ 未找到license_client.py文件")
            return False
        
        # 备份原文件
        shutil.copy2('license_client.py', 'license_client.py.backup_version_fix')
        print("✅ 已备份license_client.py")
        
        # 读取文件内容
        with open('license_client.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 添加版本管理器导入
        if 'from version_manager import get_current_version' not in content:
            # 在文件开头添加导入
            import_line = "from version_manager import get_current_version, is_auto_update_enabled\\n"
            
            # 找到第一个import语句的位置
            lines = content.split('\\n')
            insert_index = 0
            for i, line in enumerate(lines):
                if line.strip().startswith('import ') or line.strip().startswith('from '):
                    insert_index = i
                    break
            
            lines.insert(insert_index, import_line.strip())
            content = '\\n'.join(lines)
        
        # 替换硬编码的版本号
        old_version_line = 'current_version = "2.1.0"  # 当前版本号'
        new_version_line = 'current_version = get_current_version()  # 从配置文件读取版本号'
        
        if old_version_line in content:
            content = content.replace(old_version_line, new_version_line)
            print("✅ 已替换硬编码版本号")
        
        # 添加自动更新开关检查
        check_update_pattern = 'if check_and_update(self.root, current_version, license_key, device_id):'
        new_check_pattern = '''# 检查是否启用自动更新
                    if not is_auto_update_enabled():
                        logger.debug("自动更新已禁用")
                        return
                    
                    if check_and_update(self.root, current_version, license_key, device_id):'''
        
        if check_update_pattern in content:
            content = content.replace(check_update_pattern, new_check_pattern)
            print("✅ 已添加自动更新开关检查")
        
        # 保存修改后的文件
        with open('license_client.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ license_client.py修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 修复license_client.py失败: {e}")
        return False

def fix_auto_updater():
    """修复auto_updater.py中的版本更新逻辑"""
    print("🔧 修复auto_updater.py中的版本更新逻辑...")
    
    try:
        if not os.path.exists('auto_updater.py'):
            print("❌ 未找到auto_updater.py文件")
            return False
        
        # 备份原文件
        shutil.copy2('auto_updater.py', 'auto_updater.py.backup_version_fix')
        print("✅ 已备份auto_updater.py")
        
        # 读取文件内容
        with open('auto_updater.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 添加版本管理器导入
        if 'from version_manager import set_current_version' not in content:
            import_line = "from version_manager import set_current_version\\n"
            lines = content.split('\\n')
            
            # 在第一个import后添加
            for i, line in enumerate(lines):
                if line.strip().startswith('import ') or line.strip().startswith('from '):
                    lines.insert(i + 1, import_line.strip())
                    break
            
            content = '\\n'.join(lines)
        
        # 在更新完成后添加版本号更新
        restart_pattern = 'subprocess.Popen([sys.executable])'
        new_restart_pattern = '''# 更新版本号到配置文件
                set_current_version(new_version)
                subprocess.Popen([sys.executable])'''
        
        if restart_pattern in content and 'set_current_version' not in content:
            content = content.replace(restart_pattern, new_restart_pattern)
            print("✅ 已添加版本号更新逻辑")
        
        # 保存修改后的文件
        with open('auto_updater.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ auto_updater.py修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 修复auto_updater.py失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 版本管理机制修复工具")
    print("=" * 50)
    print("目标: 彻底解决无限更新循环问题")
    print("=" * 50)
    
    print("\\n开始修复...")
    
    # 1. 创建版本配置文件
    create_version_config()
    
    # 2. 创建版本管理器
    create_version_manager()
    
    # 3. 修复主程序
    fix_license_client()
    
    # 4. 修复更新器
    fix_auto_updater()
    
    print("\\n" + "=" * 50)
    print("🎉 版本管理机制修复完成！")
    print("=" * 50)
    
    print("\\n📋 修复内容:")
    print("1. ✅ 创建了版本配置文件 (version_config.json)")
    print("2. ✅ 创建了版本管理器 (version_manager.py)")
    print("3. ✅ 修复了主程序的版本读取逻辑")
    print("4. ✅ 修复了更新器的版本更新逻辑")
    
    print("\\n🚀 下一步:")
    print("1. 重新打包exe文件")
    print("2. 测试更新功能")
    print("3. 确认版本号正确显示")

if __name__ == "__main__":
    main()
