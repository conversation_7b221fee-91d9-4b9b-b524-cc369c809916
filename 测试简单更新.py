#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简单更新功能
模拟下载新文件并进行简单替换
"""

import os
import sys
import shutil
import tempfile
from datetime import datetime

def create_test_files():
    """创建测试文件"""
    print("🔧 创建测试文件...")
    
    # 创建一个模拟的新版本文件
    current_dir = os.getcwd()
    
    # 查找现有的exe文件
    import glob
    exe_files = glob.glob("*.exe")
    
    if not exe_files:
        print("❌ 未找到exe文件，无法进行测试")
        return None, None
    
    original_exe = exe_files[0]
    test_new_file = "test_new_version.exe"
    
    # 复制原文件作为"新版本"
    try:
        shutil.copy2(original_exe, test_new_file)
        print(f"✅ 创建测试新文件: {test_new_file}")
        
        # 修改文件时间以示区别
        import time
        current_time = time.time()
        os.utime(test_new_file, (current_time, current_time))
        
        return original_exe, test_new_file
    except Exception as e:
        print(f"❌ 创建测试文件失败: {e}")
        return None, None

def test_simple_replace():
    """测试简单替换功能"""
    print("🧪 测试简单文件替换功能")
    print("=" * 50)
    
    # 创建测试文件
    original_exe, test_new_file = create_test_files()
    if not original_exe or not test_new_file:
        return False
    
    try:
        # 导入更新器
        from auto_updater import AutoUpdater
        
        # 创建更新器实例
        updater = AutoUpdater()
        
        print(f"原文件: {original_exe}")
        print(f"新文件: {test_new_file}")
        
        # 备份原文件
        backup_file = f"{original_exe}.test_backup"
        shutil.copy2(original_exe, backup_file)
        print(f"✅ 已备份原文件到: {backup_file}")
        
        # 测试简单替换
        print("\n开始测试替换...")
        result = updater._simple_file_replace(test_new_file, original_exe)
        
        if result:
            print("✅ 替换脚本创建成功")
            print("⚠️ 注意: 实际替换将在3秒后执行")
            print("💡 如果要取消，请立即关闭新打开的控制台窗口")
            
            # 等待一段时间让用户看到结果
            import time
            time.sleep(5)
            
            # 检查是否有备份文件生成
            import glob
            backup_files = glob.glob(f"{original_exe}.backup_*")
            if backup_files:
                print(f"✅ 发现新的备份文件: {backup_files}")
            
            return True
        else:
            print("❌ 替换脚本创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        # 清理测试文件
        try:
            if os.path.exists(test_new_file):
                os.remove(test_new_file)
                print(f"✅ 已清理测试文件: {test_new_file}")
        except:
            pass

def check_update_method():
    """检查当前的更新方法"""
    print("🔍 检查当前更新方法")
    print("=" * 50)
    
    try:
        from auto_updater import AutoUpdater
        
        # 检查apply_update方法
        import inspect
        source = inspect.getsource(AutoUpdater.apply_update)
        
        if "_simple_file_replace" in source:
            print("✅ 当前使用简单文件替换方法")
            print("📝 更新流程:")
            print("   1. 下载新文件")
            print("   2. 创建替换脚本")
            print("   3. 退出当前程序")
            print("   4. 脚本等待3秒后替换文件")
            print("   5. 自动启动新程序")
        else:
            print("⚠️ 当前仍使用复杂的更新脚本方法")
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def main():
    """主函数"""
    print("🔧 简单更新功能测试工具")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 检查更新方法
    check_update_method()
    print()
    
    # 询问是否进行测试
    choice = input("是否进行实际测试? (y/n): ").lower().strip()
    if choice in ['y', 'yes', '是']:
        test_simple_replace()
    else:
        print("跳过实际测试")
    
    print("\n" + "=" * 50)
    print("🎯 测试完成")
    print("💡 如果测试成功，说明新的简单更新方法可以正常工作")
    print("💡 这种方法避免了复杂的进程管理和路径问题")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
