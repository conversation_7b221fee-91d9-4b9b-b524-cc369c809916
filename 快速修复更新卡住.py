#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复更新卡住问题
一键解决exe文件卡在更新界面的问题
"""

import os
import sys
import glob
import shutil
import subprocess
import time

def main():
    print("🔧 快速修复更新卡住问题")
    print("=" * 50)
    
    # 1. 终止可能的残留进程
    print("1. 终止残留进程...")
    try:
        subprocess.run(['taskkill', '/f', '/im', '亚马逊蓝图工具.exe'], 
                      capture_output=True, text=True)
        print("   ✅ 已终止相关进程")
    except:
        print("   ⚠️ 未找到运行中的进程")
    
    # 2. 查找并恢复备份文件
    print("\n2. 查找备份文件...")
    backup_files = glob.glob("*.backup")
    if backup_files:
        backup_file = backup_files[0]
        target_file = backup_file.replace('.backup', '')
        
        try:
            if os.path.exists(target_file):
                os.remove(target_file)
            shutil.copy2(backup_file, target_file)
            print(f"   ✅ 已恢复: {target_file}")
        except Exception as e:
            print(f"   ❌ 恢复失败: {e}")
    else:
        print("   ⚠️ 未找到备份文件")
    
    # 3. 清理更新状态
    print("\n3. 清理更新状态...")
    status_files = [
        os.path.join(os.path.expanduser("~"), ".amazon_last_update.json"),
        os.path.join(os.path.expanduser("~"), "update_log.txt")
    ]
    
    for status_file in status_files:
        if os.path.exists(status_file):
            try:
                os.remove(status_file)
                print(f"   ✅ 已清理: {os.path.basename(status_file)}")
            except:
                pass
    
    # 4. 尝试启动程序
    print("\n4. 尝试启动程序...")
    exe_files = glob.glob("*.exe")
    if exe_files:
        exe_file = exe_files[0]
        try:
            subprocess.Popen([exe_file])
            print(f"   ✅ 已启动: {exe_file}")
            print("\n🎉 修复完成！程序应该已经正常启动")
        except Exception as e:
            print(f"   ❌ 启动失败: {e}")
            print("\n💡 建议:")
            print("   - 检查文件是否损坏")
            print("   - 尝试重新下载程序")
    else:
        print("   ❌ 未找到exe文件")
        print("\n💡 建议: 重新下载程序")
    
    # 5. 路径建议
    current_path = os.getcwd()
    if any(char in current_path for char in ['(', ')', ' ', '中', '文']):
        print("\n⚠️ 路径问题提醒:")
        print(f"   当前路径: {current_path}")
        print("   建议将程序移动到简单的英文路径，如: C:\\AmazonTool\\")
        print("   这样可以避免中文路径导致的问题")
    
    print("\n" + "=" * 50)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
