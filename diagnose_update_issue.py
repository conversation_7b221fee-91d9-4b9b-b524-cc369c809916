#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断更新卡住问题
"""

import os
import sys
import psutil
import json
from datetime import datetime

def check_running_processes():
    """检查是否有相关进程在运行"""
    print("🔍 检查运行中的进程")
    print("=" * 50)
    
    target_names = ['license_client.exe', 'amazon', 'blueprint']
    found_processes = []
    
    try:
        for proc in psutil.process_iter(['pid', 'name', 'exe', 'cmdline']):
            try:
                proc_name = proc.info['name'].lower()
                proc_exe = proc.info['exe'] or ''
                
                for target in target_names:
                    if target in proc_name or target in proc_exe.lower():
                        found_processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'exe': proc_exe
                        })
                        break
            except:
                continue
                
        if found_processes:
            print("❌ 发现相关进程仍在运行:")
            for proc in found_processes:
                print(f"  PID: {proc['pid']}, 名称: {proc['name']}")
                print(f"  路径: {proc['exe']}")
            print("\n💡 建议：手动结束这些进程后重试")
        else:
            print("✅ 没有发现相关进程在运行")
            
    except Exception as e:
        print(f"❌ 检查进程失败: {e}")

def check_update_logs():
    """检查更新日志"""
    print("\n🔍 检查更新日志")
    print("=" * 50)
    
    log_file = os.path.join(os.path.expanduser("~"), "update_log.txt")
    
    if os.path.exists(log_file):
        print(f"✅ 找到更新日志: {log_file}")
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print("📋 最近的日志内容:")
            for line in lines[-10:]:  # 显示最后10行
                print(f"  {line.strip()}")
                
        except Exception as e:
            print(f"❌ 读取日志失败: {e}")
    else:
        print("❌ 没有找到更新日志文件")

def check_temp_files():
    """检查临时文件"""
    print("\n🔍 检查临时文件")
    print("=" * 50)
    
    import tempfile
    temp_dir = tempfile.gettempdir()
    
    temp_files = []
    try:
        for file in os.listdir(temp_dir):
            if 'amazon' in file.lower() or 'blueprint' in file.lower() or 'update' in file.lower():
                file_path = os.path.join(temp_dir, file)
                if os.path.isfile(file_path):
                    temp_files.append(file_path)
    except:
        pass
    
    if temp_files:
        print("📁 发现相关临时文件:")
        for file in temp_files:
            try:
                size = os.path.getsize(file)
                mtime = datetime.fromtimestamp(os.path.getmtime(file))
                print(f"  {file} ({size} bytes, {mtime})")
            except:
                print(f"  {file}")
    else:
        print("✅ 没有发现相关临时文件")

def check_version_config():
    """检查版本配置文件"""
    print("\n🔍 检查版本配置")
    print("=" * 50)
    
    config_dir = os.path.join(os.path.expanduser("~"), "AppData", "Local", "AmazonLicenseClient")
    config_file = os.path.join(config_dir, "version_config.json")
    
    if os.path.exists(config_file):
        print(f"✅ 找到版本配置文件: {config_file}")
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print("📋 配置内容:")
            for key, value in config.items():
                print(f"  {key}: {value}")
                
        except Exception as e:
            print(f"❌ 读取配置失败: {e}")
    else:
        print("❌ 没有找到版本配置文件")

def check_update_scripts():
    """检查更新脚本"""
    print("\n🔍 检查更新脚本")
    print("=" * 50)
    
    import tempfile
    temp_dir = tempfile.gettempdir()
    script_file = os.path.join(temp_dir, "update_script.py")
    
    if os.path.exists(script_file):
        print(f"✅ 找到更新脚本: {script_file}")
        try:
            mtime = datetime.fromtimestamp(os.path.getmtime(script_file))
            print(f"📅 脚本创建时间: {mtime}")
            
            # 检查脚本是否还在运行
            script_running = False
            try:
                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    try:
                        cmdline = ' '.join(proc.info['cmdline'] or [])
                        if 'update_script.py' in cmdline:
                            script_running = True
                            print(f"🔄 更新脚本仍在运行 (PID: {proc.info['pid']})")
                            break
                    except:
                        continue
            except:
                pass
                
            if not script_running:
                print("❌ 更新脚本已停止运行")
                
        except Exception as e:
            print(f"❌ 检查脚本失败: {e}")
    else:
        print("❌ 没有找到更新脚本")

def provide_solutions():
    """提供解决方案"""
    print("\n💡 解决方案建议")
    print("=" * 50)
    
    print("1. 🔄 手动重启程序:")
    print("   - 关闭当前卡住的更新对话框")
    print("   - 在任务管理器中结束所有相关进程")
    print("   - 重新启动程序")
    
    print("\n2. 🛠️ 手动完成更新:")
    print("   - 检查临时文件夹中是否有新版本文件")
    print("   - 手动替换exe文件")
    print("   - 更新版本配置文件")
    
    print("\n3. 🔧 重新生成exe:")
    print("   - 运行 build_gui_advanced.py")
    print("   - 生成包含修复的新版本")
    
    print("\n4. 📞 如果问题持续:")
    print("   - 查看更新日志了解具体错误")
    print("   - 检查文件权限")
    print("   - 确保没有杀毒软件阻止文件替换")

def main():
    """主诊断函数"""
    print("🚨 更新卡住问题诊断工具")
    print("=" * 60)
    
    check_running_processes()
    check_update_logs()
    check_temp_files()
    check_version_config()
    check_update_scripts()
    provide_solutions()
    
    print("\n" + "=" * 60)
    print("🎯 诊断完成")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
