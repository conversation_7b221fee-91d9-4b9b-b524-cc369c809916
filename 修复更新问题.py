#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复更新问题脚本
解决exe文件卡在更新界面的问题
"""

import os
import sys
import json
import glob
import shutil
import subprocess
import time
from datetime import datetime
from pathlib import Path

def kill_related_processes():
    """终止相关进程"""
    print("🔄 终止相关进程...")
    
    try:
        import psutil
        
        target_names = ["亚马逊蓝图工具.exe", "python.exe"]
        killed_count = 0
        
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                proc_info = proc.info
                if any(name.lower() in proc_info['name'].lower() for name in target_names):
                    # 检查是否是当前目录下的程序
                    if proc_info['exe'] and os.path.dirname(proc_info['exe']) == os.getcwd():
                        print(f"   终止进程: {proc_info['name']} (PID: {proc_info['pid']})")
                        proc.terminate()
                        killed_count += 1
            except:
                continue
        
        if killed_count > 0:
            print(f"   已终止 {killed_count} 个进程")
            time.sleep(2)  # 等待进程完全退出
        else:
            print("   未发现需要终止的进程")
            
    except ImportError:
        print("   psutil未安装，跳过进程检查")

def restore_from_backup():
    """从备份恢复程序"""
    print("📦 检查并恢复备份文件...")
    
    # 查找备份文件
    backup_files = glob.glob("*.backup")
    if not backup_files:
        backup_files = glob.glob("*.exe.backup")
    
    if backup_files:
        backup_file = backup_files[0]  # 使用第一个找到的备份
        print(f"   发现备份文件: {backup_file}")
        
        # 确定目标文件名
        if backup_file.endswith('.exe.backup'):
            target_file = backup_file[:-7]  # 移除 .backup
        else:
            target_file = backup_file[:-7] + '.exe'  # 移除 .backup 并添加 .exe
        
        try:
            # 如果目标文件存在，先删除
            if os.path.exists(target_file):
                os.remove(target_file)
                print(f"   删除损坏的文件: {target_file}")
            
            # 恢复备份
            shutil.copy2(backup_file, target_file)
            print(f"   ✅ 成功恢复到: {target_file}")
            
            # 验证恢复的文件
            if os.path.exists(target_file):
                size_mb = os.path.getsize(target_file) / (1024 * 1024)
                print(f"   文件大小: {size_mb:.1f}MB")
                return target_file
            else:
                print("   ❌ 恢复失败，文件不存在")
                return None
                
        except Exception as e:
            print(f"   ❌ 恢复失败: {e}")
            return None
    else:
        print("   ❌ 未找到备份文件")
        return None

def clean_temp_files():
    """清理临时文件"""
    print("🗑️ 清理临时文件...")
    
    temp_patterns = [
        "update_*.py",
        "temp_*.exe",
        "*.tmp"
    ]
    
    cleaned_count = 0
    for pattern in temp_patterns:
        temp_files = glob.glob(pattern)
        for temp_file in temp_files:
            try:
                os.remove(temp_file)
                print(f"   删除: {temp_file}")
                cleaned_count += 1
            except Exception as e:
                print(f"   删除失败 {temp_file}: {e}")
    
    if cleaned_count > 0:
        print(f"   ✅ 清理了 {cleaned_count} 个临时文件")
    else:
        print("   未找到需要清理的临时文件")

def reset_update_status():
    """重置更新状态"""
    print("🔄 重置更新状态...")
    
    # 清理更新状态文件
    status_files = [
        os.path.join(os.path.expanduser("~"), ".amazon_last_update.json"),
        os.path.join(os.path.expanduser("~"), "update_log.txt")
    ]
    
    for status_file in status_files:
        if os.path.exists(status_file):
            try:
                os.remove(status_file)
                print(f"   删除状态文件: {status_file}")
            except Exception as e:
                print(f"   删除失败 {status_file}: {e}")
    
    # 重置外部配置文件中的更新标记
    try:
        config_dir = os.path.join(os.path.expanduser("~"), "AppData", "Local", "AmazonLicenseClient")
        external_config_file = os.path.join(config_dir, "version_config.json")
        
        if os.path.exists(external_config_file):
            with open(external_config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 移除更新相关的标记
            if 'last_updated' in config_data:
                del config_data['last_updated']
            
            with open(external_config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            print(f"   重置外部配置: {external_config_file}")
    except Exception as e:
        print(f"   重置外部配置失败: {e}")

def test_program(exe_file):
    """测试程序是否能正常启动"""
    print(f"🧪 测试程序启动: {exe_file}")
    
    if not os.path.exists(exe_file):
        print("   ❌ 程序文件不存在")
        return False
    
    try:
        # 尝试启动程序（不等待）
        process = subprocess.Popen([exe_file], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE)
        
        # 等待短时间看是否能启动
        time.sleep(3)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("   ✅ 程序启动成功")
            # 不要终止进程，让用户使用
            return True
        else:
            stdout, stderr = process.communicate()
            print("   ❌ 程序启动失败")
            if stderr:
                print(f"   错误信息: {stderr.decode('utf-8', errors='ignore')}")
            return False
            
    except Exception as e:
        print(f"   ❌ 启动测试失败: {e}")
        return False

def suggest_path_fix():
    """建议路径修复"""
    current_path = os.getcwd()
    
    # 检查路径是否有问题
    problematic_chars = ['(', ')', ' ', '中', '文']
    has_issues = any(char in current_path for char in problematic_chars)
    
    if has_issues:
        print("\n" + "=" * 60)
        print("⚠️ 路径问题建议")
        print("=" * 60)
        print(f"当前路径: {current_path}")
        print("\n建议操作:")
        print("1. 创建一个简单的英文目录，如: C:\\AmazonTool\\")
        print("2. 将所有程序文件复制到新目录")
        print("3. 从新目录运行程序")
        print("4. 这样可以避免中文路径和特殊字符导致的问题")

def main():
    """主修复函数"""
    print("🔧 亚马逊蓝图工具 - 更新问题修复")
    print(f"修复时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 步骤1: 终止相关进程
    kill_related_processes()
    
    # 步骤2: 清理临时文件
    clean_temp_files()
    
    # 步骤3: 重置更新状态
    reset_update_status()
    
    # 步骤4: 尝试从备份恢复
    restored_file = restore_from_backup()
    
    # 步骤5: 测试程序
    if restored_file:
        if test_program(restored_file):
            print("\n✅ 修复成功！程序已启动")
        else:
            print("\n❌ 程序恢复但无法启动，可能需要重新下载")
    else:
        # 查找现有的exe文件
        exe_files = glob.glob("*.exe")
        if exe_files:
            exe_file = exe_files[0]
            if test_program(exe_file):
                print("\n✅ 现有程序可以正常使用")
            else:
                print("\n❌ 现有程序无法启动，建议重新下载")
        else:
            print("\n❌ 未找到可执行文件，需要重新下载程序")
    
    # 步骤6: 路径建议
    suggest_path_fix()
    
    print("\n" + "=" * 60)
    print("🎯 修复完成")
    print("=" * 60)
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
