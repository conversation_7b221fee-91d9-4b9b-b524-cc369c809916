# 🔧 exe文件管理工具 - 使用说明

## 📋 功能概述

`exe文件管理工具.py` 是一个用于管理服务器上exe文件版本的图形化工具，支持上传、下载、删除和管理exe文件版本。

## 🆕 新增功能

### ✅ 删除功能
- **🗑️ 删除选中版本**：删除服务器上的指定版本文件
- **🗑️ 清空所有版本**：删除服务器上的所有版本文件（危险操作）
- **📊 服务器存储信息**：查看服务器存储使用情况

### ✅ 右键菜单
- **📥 下载此版本**：快速下载选中版本
- **🗑️ 删除此版本**：快速删除选中版本
- **📋 复制版本号**：复制版本号到剪贴板
- **ℹ️ 版本详情**：查看版本详细信息
- **🔄 刷新列表**：刷新版本列表

## 🚀 使用方法

### 1. 启动工具
```bash
python exe文件管理工具.py
```

### 2. 基本操作

#### 📤 上传新版本
1. 点击"📁 选择exe文件"选择要上传的exe文件
2. 输入版本号（如：2.1.1）
3. 输入更新说明（可选）
4. 点击"📤 上传版本"

#### 📥 下载版本
1. 在版本列表中选择要下载的版本
2. 点击"📥 下载选中版本"或右键选择"📥 下载此版本"
3. 选择保存位置

#### 🗑️ 删除版本
1. 在版本列表中选择要删除的版本
2. 点击"🗑️ 删除选中版本"或右键选择"🗑️ 删除此版本"
3. 确认删除操作

### 3. 高级管理

#### 🗑️ 清空所有版本
⚠️ **危险操作**：将删除服务器上的所有版本文件
1. 点击"🗑️ 清空所有版本"
2. 通过多重确认对话框
3. 等待清空完成

#### 📊 查看存储信息
1. 点击"📊 服务器存储信息"
2. 查看文件统计、版本列表、磁盘使用情况

## 🔧 管理员认证

工具使用管理员权限访问服务器：
- **管理员密钥**：`ADMIN_BYPASS`
- **设备ID**：`ADMIN-DEVICE-001`
- **服务器地址**：`http://198.23.135.176:5000`

## 📋 界面说明

### 左侧 - 版本列表
- 显示所有可用版本
- 包含版本号、文件大小、上传时间
- 支持右键菜单操作

### 右侧 - 操作面板

#### 📤 上传新版本
- 版本号输入框
- 更新说明文本框
- 文件选择和上传按钮

#### 🔧 版本管理
- 刷新列表
- 下载选中版本
- 删除选中版本
- 测试更新API
- 初始化服务器

#### 🔧 高级管理
- 清空所有版本
- 服务器存储信息

#### 🌐 服务器信息
- 服务器地址
- 连接状态

## ⚠️ 注意事项

### 🚨 删除操作
1. **不可恢复**：删除操作是永久性的，无法恢复
2. **影响客户端**：删除后客户端无法下载该版本
3. **多重确认**：重要操作需要多次确认

### 🔒 权限管理
1. **管理员权限**：工具使用管理员权限，请谨慎操作
2. **网络连接**：需要稳定的网络连接到服务器
3. **超时设置**：大文件操作可能需要较长时间

### 📁 文件管理
1. **版本号格式**：建议使用语义化版本号（如：2.1.1）
2. **文件大小**：注意服务器存储空间限制
3. **备份策略**：重要版本建议本地备份

## 🔄 与更新循环问题的关系

此工具与之前解决的无限更新循环问题相关：

1. **版本号管理**：确保上传的exe文件版本号正确
2. **服务器同步**：保持服务器版本与客户端版本一致
3. **测试验证**：上传新版本后可以测试更新API

### 建议操作流程
1. 使用修复后的 `build_gui_advanced.py` 打包exe（设置正确版本号）
2. 使用此工具上传新版本到服务器
3. 测试更新API确认功能正常
4. 客户端测试确认不再出现无限循环

## 🛠️ 故障排除

### 连接问题
- 检查网络连接
- 确认服务器地址正确
- 检查防火墙设置

### 上传失败
- 检查文件是否被占用
- 确认文件大小不超过限制
- 检查版本号格式

### 删除失败
- 确认版本存在
- 检查管理员权限
- 确认服务器支持删除功能

## 📞 技术支持

如遇到问题：
1. 查看底部状态栏的错误信息
2. 检查服务器连接状态
3. 确认管理员认证信息
4. 查看控制台输出的详细错误信息

**工具已完全支持删除功能，可以安全地管理服务器上的exe文件版本！**
