#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紧急恢复程序
从备份文件恢复丢失的exe程序
"""

import os
import sys
import shutil
import subprocess
from datetime import datetime

def recover_from_backup():
    """从备份恢复程序"""
    print("🚨 紧急恢复程序")
    print("=" * 50)
    print("检测到exe文件丢失，尝试从备份恢复...")
    
    # 查找备份文件
    backup_file = "license_client.py.backup"
    
    if not os.path.exists(backup_file):
        print(f"❌ 未找到备份文件: {backup_file}")
        return False
    
    print(f"✅ 找到备份文件: {backup_file}")
    
    # 检查备份文件大小
    size_mb = os.path.getsize(backup_file) / (1024 * 1024)
    print(f"备份文件大小: {size_mb:.1f}MB")
    
    if size_mb < 0.01:  # 小于10KB
        print("❌ 备份文件太小，可能已损坏")
        return False
    
    # 尝试从备份恢复
    try:
        # 这个备份文件是Python源码，我们需要重新打包
        print("📦 从Python源码重新生成exe文件...")
        
        # 检查是否有PyInstaller
        try:
            import PyInstaller
            print("✅ 找到PyInstaller")
        except ImportError:
            print("❌ 未安装PyInstaller，尝试安装...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
                print("✅ PyInstaller安装成功")
            except:
                print("❌ PyInstaller安装失败")
                return False
        
        # 恢复源码文件
        source_file = "license_client.py"
        shutil.copy2(backup_file, source_file)
        print(f"✅ 恢复源码文件: {source_file}")
        
        # 使用PyInstaller重新打包
        print("🔨 重新打包程序...")
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",
            "--windowed", 
            "--name", "亚马逊蓝图工具",
            source_file
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 打包成功")
            
            # 查找生成的exe文件
            dist_dir = "dist"
            if os.path.exists(dist_dir):
                exe_files = [f for f in os.listdir(dist_dir) if f.endswith('.exe')]
                if exe_files:
                    exe_file = exe_files[0]
                    src_path = os.path.join(dist_dir, exe_file)
                    dst_path = exe_file
                    
                    # 移动exe文件到当前目录
                    shutil.move(src_path, dst_path)
                    print(f"✅ exe文件已恢复: {dst_path}")
                    
                    # 清理临时文件
                    try:
                        shutil.rmtree("build")
                        shutil.rmtree("dist")
                        if os.path.exists("亚马逊蓝图工具.spec"):
                            os.remove("亚马逊蓝图工具.spec")
                        print("✅ 清理临时文件完成")
                    except:
                        pass
                    
                    return dst_path
        else:
            print("❌ 打包失败")
            print(f"错误信息: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 恢复失败: {e}")
        return False

def create_simple_launcher():
    """创建简单的启动器"""
    print("🔧 创建简单启动器...")
    
    launcher_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
亚马逊蓝图工具启动器
"""

import os
import sys

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    # 尝试导入并运行主程序
    from license_client import main
    main()
except ImportError as e:
    print(f"导入失败: {e}")
    print("请确保license_client.py文件存在")
    input("按回车键退出...")
except Exception as e:
    print(f"运行失败: {e}")
    input("按回车键退出...")
'''
    
    launcher_file = "启动亚马逊蓝图工具.py"
    try:
        with open(launcher_file, 'w', encoding='utf-8') as f:
            f.write(launcher_content)
        print(f"✅ 创建启动器: {launcher_file}")
        return launcher_file
    except Exception as e:
        print(f"❌ 创建启动器失败: {e}")
        return None

def suggest_path_move():
    """建议移动到简单路径"""
    print("\n" + "=" * 50)
    print("⚠️ 重要建议")
    print("=" * 50)
    print("当前路径包含中文字符和特殊符号，这是导致更新失败的主要原因")
    print(f"当前路径: {os.getcwd()}")
    print("\n强烈建议:")
    print("1. 创建简单的英文目录: C:\\AmazonTool\\")
    print("2. 将所有文件移动到新目录")
    print("3. 从新目录运行程序")
    print("4. 这样可以彻底避免路径问题")

def main():
    """主函数"""
    print("🚨 亚马逊蓝图工具 - 紧急恢复")
    print(f"恢复时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 尝试从备份恢复
    recovered_exe = recover_from_backup()
    
    if recovered_exe:
        print(f"\n🎉 恢复成功！")
        print(f"程序文件: {recovered_exe}")
        
        # 尝试启动恢复的程序
        try:
            subprocess.Popen([recovered_exe])
            print("✅ 程序已启动")
        except Exception as e:
            print(f"⚠️ 启动失败: {e}")
            print("请手动双击运行程序")
    else:
        print("\n⚠️ 无法从备份恢复exe文件")
        
        # 创建Python启动器作为备选方案
        launcher = create_simple_launcher()
        if launcher:
            print(f"✅ 已创建Python启动器: {launcher}")
            print("可以通过运行这个Python文件来启动程序")
            
            # 尝试启动Python版本
            try:
                subprocess.Popen([sys.executable, launcher])
                print("✅ Python版本已启动")
            except Exception as e:
                print(f"⚠️ 启动失败: {e}")
    
    # 路径建议
    suggest_path_move()
    
    print("\n" + "=" * 50)
    print("🎯 恢复完成")
    print("=" * 50)
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
